{"kind": "collectionType", "collectionName": "exercise_types", "info": {"singularName": "exercise-type", "pluralName": "exercise-types", "displayName": "Exercise_type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"questions": {"type": "relation", "relation": "oneToMany", "target": "api::question.question", "mappedBy": "exercise_type"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "enumeration", "enum": ["hieu_biet", "van_dung"]}, "point": {"type": "integer"}}}